import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Row,
  Col,
  Card,
  <PERSON><PERSON>,
  <PERSON>ge,
  <PERSON><PERSON>,
  Spinner,
  Table,
  Tabs,
  Tab,
  Form,
  InputGroup,
  Dropdown,
  ButtonGroup,
  Modal
} from 'react-bootstrap';
import { FaSearch, FaFilter, FaEye, FaEdit, FaCalendarAlt, FaDownload, FaSort, FaSortUp, FaSortDown } from 'react-icons/fa';
import { fetchOrders, fetchOrderStatuses } from '../../services/orderService';

// Helper function to map status codes to labels
const getStatusLabel = (status) => {
  const statusMap = {
    en_attente: 'En attente',
    confirmee: 'Confirmée',
    en_preparation: 'En préparation',
    expediee: 'Expédiée',
    livree: 'Livrée',
    annulee: 'Annulée',
    remboursee: 'Remboursée',
    retournee: 'Retournée'
  };

  return statusMap[status] || status || 'En attente';
};

// Helper function to get status code from tab
const getStatusCodeFromTab = (tab) => {
  const tabToStatusMap = {
    pending: 'en_attente',
    confirmed: 'confirmee',
    processing: 'en_preparation',
    shipped: 'expediee',
    delivered: 'livree',
    cancelled: 'annulee',
    refunded: 'remboursee',
    returned: 'retournee'
  };

  return tabToStatusMap[tab];
};

const OrderList = () => {
  const navigate = useNavigate();

  // State
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [orderStatuses, setOrderStatuses] = useState([]);
  const [filterParams, setFilterParams] = useState({
    user_id: '',
    date_debut: '',
    date_fin: '',
    page: 1,
    per_page: 15
  });
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination state
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0,
    from: 0,
    to: 0
  });

  // Action states
  const [actionLoading, setActionLoading] = useState({});
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [newStatus, setNewStatus] = useState('');
  const [statusNotes, setStatusNotes] = useState('');

  // Helper function to get status code from tab
  const getStatusCodeFromTab = (tab) => {
    const statusMap = {
      pending: 'en_attente',
      confirmed: 'confirmee',
      processing: 'en_preparation',
      shipped: 'expediee',
      delivered: 'livree',
      cancelled: 'annulee',
      refunded: 'remboursee',
      returned: 'retournee'
    };
    return statusMap[tab];
  };

  // Helper function to get status label from code
  const getStatusLabel = (statusCode) => {
    const statusMap = {
      en_attente: 'En attente',
      confirmee: 'Confirmée',
      en_preparation: 'En préparation',
      expediee: 'Expédiée',
      livree: 'Livrée',
      annulee: 'Annulée',
      remboursee: 'Remboursée',
      retournee: 'Retournée'
    };
    return statusMap[statusCode] || statusCode || 'En attente';
  };

  // Debug authentication state
  const debugAuthState = () => {
    const authData = {
      accessToken: localStorage.getItem('access_token'),
      refreshToken: localStorage.getItem('refresh_token'),
      idToken: localStorage.getItem('id_token'),
      userData: localStorage.getItem('user_data'),
      userLoggedOut: sessionStorage.getItem('user_logged_out'),
      apiUrl: import.meta.env.VITE_REACT_APP_API_URL || 'Not set'
    };

    console.log('🔐 Auth Debug State:', {
      hasAccessToken: !!authData.accessToken,
      hasRefreshToken: !!authData.refreshToken,
      hasIdToken: !!authData.idToken,
      hasUserData: !!authData.userData,
      userLoggedOut: authData.userLoggedOut,
      apiUrl: authData.apiUrl,
      tokenPreview: authData.accessToken ? authData.accessToken.substring(0, 20) + '...' : 'None'
    });

    return authData;
  };

  // Load orders with retry mechanism
  const loadOrders = async (retryCount = 0) => {
    setLoading(true);
    setError('');

    // Debug authentication before making API call
    debugAuthState();

    try {
      // Build parameters according to API documentation
      const params = {
        page: filterParams.page,
        per_page: filterParams.per_page,
        sort_by: sortField,
        sort_direction: sortDirection
      };

      // Add optional filters
      if (filterParams.user_id) params.user_id = filterParams.user_id;
      if (filterParams.date_debut) params.date_from = filterParams.date_debut;
      if (filterParams.date_fin) params.date_to = filterParams.date_fin;
      if (activeTab !== 'all') params.status = getStatusCodeFromTab(activeTab);
      if (searchTerm) params.search = searchTerm;

      const response = await fetchOrders(params);

      console.log('📋 OrderList received response:', {
        hasResponse: !!response,
        responseType: typeof response,
        responseKeys: response ? Object.keys(response) : [],
        hasData: !!response?.data,
        dataType: typeof response?.data,
        dataLength: Array.isArray(response?.data) ? response.data.length : 'not array',
        dataKeys: response?.data ? Object.keys(response.data) : []
      });

      // Handle different response structures according to API documentation
      let ordersData = [];
      let paginationData = {};

      if (response) {
        // Check if response has data property (paginated response)
        if (response.data && Array.isArray(response.data.data)) {
          // Paginated response structure: { data: { data: [...], current_page: 1, ... } }
          ordersData = response.data.data;
          paginationData = response.data;
        } else if (response.data && Array.isArray(response.data)) {
          // Direct data array: { data: [...] }
          ordersData = response.data;
          paginationData = response;
        } else if (Array.isArray(response)) {
          // Direct array response: [...]
          ordersData = response;
          paginationData = { current_page: 1, last_page: 1, per_page: ordersData.length, total: ordersData.length };
        } else {
          console.warn('⚠️ Unexpected response structure:', response);
        }
      }

      console.log('📋 Processed orders data:', {
        ordersCount: ordersData.length,
        pagination: paginationData
      });

      setOrders(ordersData);
      setPagination({
        current_page: paginationData.current_page || 1,
        last_page: paginationData.last_page || 1,
        per_page: paginationData.per_page || 15,
        total: paginationData.total || ordersData.length,
        from: paginationData.from || (ordersData.length > 0 ? 1 : 0),
        to: paginationData.to || ordersData.length
      });
    } catch (e) {
      console.error('Error loading orders:', e);

      // Retry logic for network errors
      if (e.message.includes('connexion') && retryCount < 3) {
        console.log(`Retrying... Attempt ${retryCount + 1} of 3`);
        setTimeout(
          () => {
            loadOrders(retryCount + 1);
          },
          2000 * (retryCount + 1)
        ); // Exponential backoff
        return;
      }

      setError(e.message || 'Erreur lors du chargement des commandes');
      setOrders([]);
      setPagination({
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 0,
        from: 0,
        to: 0
      });
    }
    setLoading(false);
  };

  // Load order statuses
  const loadOrderStatuses = async () => {
    try {
      const data = await fetchOrderStatuses();
      setOrderStatuses(data);
    } catch (e) {
      console.error('Erreur lors du chargement des statuts de commande:', e);
    }
  };

  // Initial data load
  useEffect(() => {
    console.log('Component mounted, loading data...');
    loadOrderStatuses();
  }, []);

  // Reload orders when filter parameters change
  useEffect(() => {
    loadOrders();
  }, [filterParams, sortField, sortDirection, activeTab]);

  // Debug effect to log orders when they change
  useEffect(() => {
    console.log('Orders state updated:', orders);
    if (orders.length === 0) {
      console.log('No orders found, will try to use mock data');
    }
  }, [orders]);

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilterParams({
      ...filterParams,
      [name]: value
    });
  };

  // Apply filters
  const applyFilters = (e) => {
    e.preventDefault();
    loadOrders();
  };

  // Reset filters
  const resetFilters = () => {
    setFilterParams({
      user_id: '',
      date_debut: '',
      date_fin: '',
      page: 1,
      per_page: 15
    });
    setSearchTerm('');
    setActiveTab('all');
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return <FaSort className="text-muted" />;
    return sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />;
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  // Format price
  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  };

  // Get status badge variant
  const getStatusVariant = (status) => {
    const statusColorMap = {
      en_attente: 'warning',
      confirmee: 'info',
      en_preparation: 'primary',
      expediee: 'primary',
      livree: 'success',
      annulee: 'danger',
      remboursee: 'secondary',
      retournee: 'secondary'
    };

    return statusColorMap[status] || 'secondary';
  };

  // Filter orders by status for tabs - simplified since API handles filtering
  const getFilteredOrders = () => {
    if (!orders || orders.length === 0) {
      console.log('No orders to filter');
      return [];
    }

    // Filter out null/invalid orders
    const validOrders = orders.filter((order) => {
      if (!order) {
        console.warn('Found null order, filtering out');
        return false;
      }

      // Basic validation - only check essential properties
      if (!order.id) {
        console.warn('Order missing ID:', order);
        return false;
      }

      return true;
    });

    console.log(`Returning ${validOrders.length} valid orders`);
    return validOrders;
  };

  // Pagination handlers
  const handlePageChange = (page) => {
    console.log(`Changing to page ${page}, current: ${pagination.current_page}, max: ${pagination.last_page}`);
    if (page >= 1 && page <= pagination.last_page && page !== pagination.current_page) {
      setFilterParams((prev) => ({ ...prev, page }));
    }
  };

  const handlePerPageChange = (perPage) => {
    console.log(`Changing per page to ${perPage}`);
    setFilterParams((prev) => ({ ...prev, per_page: perPage, page: 1 }));
  };

  // Direct page input handler
  const [pageInputValue, setPageInputValue] = useState('1');

  const handleDirectPageInput = (value) => {
    setPageInputValue(value);
    const page = parseInt(value);
    if (!isNaN(page) && page >= 1 && page <= pagination.last_page) {
      handlePageChange(page);
    }
  };

  // Update page input value when pagination changes
  React.useEffect(() => {
    setPageInputValue(pagination.current_page.toString());
  }, [pagination.current_page]);

  // Action handlers
  const handleStatusChange = (order) => {
    setSelectedOrder(order);
    setNewStatus(order._original?.status || 'en_attente');
    setStatusNotes('');
    setShowStatusModal(true);
  };

  const handleUpdateStatus = async () => {
    if (!selectedOrder || !newStatus) return;

    setActionLoading((prev) => ({ ...prev, [`status_${selectedOrder.id}`]: true }));
    try {
      const { updateOrderStatus } = await import('../../services/orderService');
      await updateOrderStatus(selectedOrder.id, newStatus, statusNotes);
      setShowStatusModal(false);
      loadOrders(); // Reload orders
    } catch (error) {
      setError(`Erreur lors de la mise à jour du statut: ${error.message}`);
    }
    setActionLoading((prev) => ({ ...prev, [`status_${selectedOrder.id}`]: false }));
  };

  const handleCancelOrder = async (order) => {
    if (!window.confirm('Êtes-vous sûr de vouloir annuler cette commande ?')) return;

    setActionLoading((prev) => ({ ...prev, [`cancel_${order.id}`]: true }));
    try {
      const { cancelOrder } = await import('../../services/orderService');
      await cancelOrder(order.id, "Annulée par l'administrateur");
      loadOrders(); // Reload orders
    } catch (error) {
      setError(`Erreur lors de l'annulation: ${error.message}`);
    }
    setActionLoading((prev) => ({ ...prev, [`cancel_${order.id}`]: false }));
  };

  const handleDeleteOrder = async (order) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette commande ? Cette action est irréversible.')) return;

    setActionLoading((prev) => ({ ...prev, [`delete_${order.id}`]: true }));
    try {
      const { deleteOrder } = await import('../../services/orderService');
      await deleteOrder(order.id);
      loadOrders(); // Reload orders
    } catch (error) {
      setError(`Erreur lors de la suppression: ${error.message}`);
    }
    setActionLoading((prev) => ({ ...prev, [`delete_${order.id}`]: false }));
  };

  // View order details
  const viewOrderDetails = (orderId) => {
    console.log('🔗 Navigating to order details:', orderId);
    navigate(`/app/orders/${orderId}`);
  };

  // Export orders
  const handleExportOrders = () => {
    // TODO: Implement export functionality
    alert("Fonctionnalité d'export en cours de développement");
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-1">Gestion des Commandes</h2>
          <p className="text-muted mb-0">Gérez et suivez toutes les commandes clients</p>
        </div>
        <div>
          <Button variant="outline-secondary" className="me-2" onClick={handleExportOrders}>
            <FaDownload className="me-2" />
            Exporter
          </Button>
          <Button variant="primary" onClick={() => navigate('/app/orders/new')}>
            Nouvelle Commande
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="d-flex align-items-center">
          <div className="flex-grow-1">{error}</div>
          <Button variant="outline-danger" size="sm" onClick={() => loadOrders()} className="ms-3">
            Réessayer
          </Button>
        </Alert>
      )}

      {/* Filters */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body>
          <Form onSubmit={applyFilters}>
            <Row>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Recherche</Form.Label>
                  <InputGroup>
                    <Form.Control
                      type="text"
                      placeholder="Rechercher..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button variant="outline-secondary">
                      <FaSearch />
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Client ID</Form.Label>
                  <Form.Control
                    type="text"
                    name="user_id"
                    value={filterParams.user_id}
                    onChange={handleFilterChange}
                    placeholder="ID du client"
                  />
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de début</Form.Label>
                  <InputGroup>
                    <Form.Control type="date" name="date_debut" value={filterParams.date_debut} onChange={handleFilterChange} />
                    <Button variant="outline-secondary">
                      <FaCalendarAlt />
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
              <Col md={3}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de fin</Form.Label>
                  <InputGroup>
                    <Form.Control type="date" name="date_fin" value={filterParams.date_fin} onChange={handleFilterChange} />
                    <Button variant="outline-secondary">
                      <FaCalendarAlt />
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
            </Row>
            <div className="d-flex justify-content-end">
              <Button variant="outline-secondary" className="me-2" onClick={resetFilters}>
                Réinitialiser
              </Button>
              <Button variant="primary" type="submit">
                <FaFilter className="me-2" />
                Filtrer
              </Button>
            </div>
          </Form>
        </Card.Body>
      </Card>

      {/* Tabs */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body className="p-0">
          <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-0 nav-tabs-custom" fill>
            <Tab eventKey="all" title="Toutes les commandes" />
            <Tab eventKey="pending" title="En attente" />
            <Tab eventKey="confirmed" title="Confirmées" />
            <Tab eventKey="processing" title="En préparation" />
            <Tab eventKey="shipped" title="Expédiées" />
            <Tab eventKey="delivered" title="Livrées" />
            <Tab eventKey="cancelled" title="Annulées" />
          </Tabs>
        </Card.Body>
      </Card>

      {/* Orders Table */}
      <Card className="shadow-sm border-0">
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3">Chargement des commandes...</p>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-5">
              <p className="mb-0">Aucune commande trouvée</p>
            </div>
          ) : (
            <Table responsive hover className="mb-0">
              <thead>
                <tr>
                  <th onClick={() => handleSort('numero_commande')} style={{ cursor: 'pointer' }}>
                    N° Commande {getSortIcon('numero_commande')}
                  </th>
                  <th onClick={() => handleSort('created_at')} style={{ cursor: 'pointer' }}>
                    Date {getSortIcon('created_at')}
                  </th>
                  <th onClick={() => handleSort('total_commande')} style={{ cursor: 'pointer' }}>
                    Total {getSortIcon('total_commande')}
                  </th>
                  <th onClick={() => handleSort('status')} style={{ cursor: 'pointer' }}>
                    Statut {getSortIcon('status')}
                  </th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {orders.map((order) => (
                  <tr key={order.id}>
                    <td>
                      <span className="fw-medium">{order.numero_commande}</span>
                    </td>
                    <td>{formatDate(order.created_at)}</td>
                    <td>
                      <span className="fw-medium">{formatPrice(order.total_commande)}</span>
                    </td>
                    <td>
                      <Badge bg={getStatusVariant(order.status)}>{getStatusLabel(order.status)}</Badge>
                    </td>
                    <td>
                      <ButtonGroup>
                        <Button variant="outline-primary" size="sm" onClick={() => viewOrderDetails(order.id)} title="Voir les détails">
                          <FaEye />
                        </Button>
                        <Dropdown>
                          <Dropdown.Toggle split variant="outline-primary" size="sm" id={`dropdown-split-${order.id}`} />
                          <Dropdown.Menu>
                            <Dropdown.Item onClick={() => handleStatusChange(order)}>
                              <FaEdit className="me-2" />
                              Changer le statut
                            </Dropdown.Item>
                            {order.status !== 'annulee' && (
                              <Dropdown.Item onClick={() => handleCancelOrder(order)} className="text-warning">
                                <i className="fas fa-ban me-2"></i>
                                Annuler la commande
                              </Dropdown.Item>
                            )}
                          </Dropdown.Menu>
                        </Dropdown>
                      </ButtonGroup>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
        <Card.Footer className="bg-white">
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              <span className="me-3">
                Affichage de {pagination.from} à {pagination.to} sur {pagination.total} commande(s)
              </span>
              <Form.Select
                size="sm"
                style={{ width: 'auto' }}
                value={pagination.per_page}
                onChange={(e) => handlePerPageChange(parseInt(e.target.value))}
              >
                <option value={10}>10 par page</option>
                <option value={15}>15 par page</option>
                <option value={25}>25 par page</option>
                <option value={50}>50 par page</option>
                <option value={100}>100 par page</option>
              </Form.Select>
            </div>

            {pagination.last_page > 1 && (
              <div className="d-flex align-items-center">
                {/* Page Navigation */}
                <nav className="me-3">
                  <ul className="pagination pagination-sm mb-0">
                    <li className={`page-item ${pagination.current_page === 1 ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => handlePageChange(1)}
                        disabled={pagination.current_page === 1}
                        title="Première page"
                      >
                        <i className="fas fa-angle-double-left"></i>
                      </button>
                    </li>
                    <li className={`page-item ${pagination.current_page === 1 ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => handlePageChange(pagination.current_page - 1)}
                        disabled={pagination.current_page === 1}
                        title="Page précédente"
                      >
                        <i className="fas fa-angle-left"></i>
                      </button>
                    </li>

                    {/* Smart page number display */}
                    {(() => {
                      const current = pagination.current_page;
                      const total = pagination.last_page;
                      const pages = [];

                      // Always show first page
                      if (current > 3) {
                        pages.push(1);
                        if (current > 4) {
                          pages.push('...');
                        }
                      }

                      // Show pages around current page
                      const start = Math.max(1, current - 2);
                      const end = Math.min(total, current + 2);

                      for (let i = start; i <= end; i++) {
                        pages.push(i);
                      }

                      // Always show last page
                      if (current < total - 2) {
                        if (current < total - 3) {
                          pages.push('...');
                        }
                        pages.push(total);
                      }

                      return pages.map((page, index) => {
                        if (page === '...') {
                          return (
                            <li key={`ellipsis-${index}`} className="page-item disabled">
                              <span className="page-link">...</span>
                            </li>
                          );
                        }

                        return (
                          <li key={page} className={`page-item ${current === page ? 'active' : ''}`}>
                            <button className="page-link" onClick={() => handlePageChange(page)} title={`Page ${page}`}>
                              {page}
                            </button>
                          </li>
                        );
                      });
                    })()}

                    <li className={`page-item ${pagination.current_page === pagination.last_page ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => handlePageChange(pagination.current_page + 1)}
                        disabled={pagination.current_page === pagination.last_page}
                        title="Page suivante"
                      >
                        <i className="fas fa-angle-right"></i>
                      </button>
                    </li>
                    <li className={`page-item ${pagination.current_page === pagination.last_page ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => handlePageChange(pagination.last_page)}
                        disabled={pagination.current_page === pagination.last_page}
                        title="Dernière page"
                      >
                        <i className="fas fa-angle-double-right"></i>
                      </button>
                    </li>
                  </ul>
                </nav>

                {/* Direct page input */}
                <div className="d-flex align-items-center">
                  <span className="text-muted me-2">Aller à:</span>
                  <Form.Control
                    type="number"
                    size="sm"
                    style={{ width: '70px' }}
                    min="1"
                    max={pagination.last_page}
                    value={pageInputValue}
                    onChange={(e) => handleDirectPageInput(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        const page = parseInt(e.target.value);
                        if (page >= 1 && page <= pagination.last_page) {
                          handlePageChange(page);
                        }
                      }
                    }}
                    onBlur={(e) => {
                      // Reset to current page if invalid input
                      const page = parseInt(e.target.value);
                      if (isNaN(page) || page < 1 || page > pagination.last_page) {
                        setPageInputValue(pagination.current_page.toString());
                      }
                    }}
                  />
                  <span className="text-muted ms-2">/ {pagination.last_page}</span>
                </div>
              </div>
            )}
          </div>
        </Card.Footer>
      </Card>

      {/* Status Change Modal */}
      <Modal show={showStatusModal} onHide={() => setShowStatusModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Changer le statut de la commande</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedOrder && (
            <div>
              <p>
                <strong>Commande:</strong> {selectedOrder.order_number}
              </p>
              <p>
                <strong>Client:</strong> {selectedOrder.customer_name}
              </p>
              <p>
                <strong>Statut actuel:</strong> <Badge bg={getStatusVariant(selectedOrder.status)}>{selectedOrder.status}</Badge>
              </p>

              <Form.Group className="mb-3">
                <Form.Label>Nouveau statut</Form.Label>
                <Form.Select value={newStatus} onChange={(e) => setNewStatus(e.target.value)}>
                  {orderStatuses.map((status) => (
                    <option key={status.id} value={status.value}>
                      {status.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Notes (optionnel)</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  value={statusNotes}
                  onChange={(e) => setStatusNotes(e.target.value)}
                  placeholder="Ajoutez des notes sur ce changement de statut..."
                />
              </Form.Group>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowStatusModal(false)}>
            Annuler
          </Button>
          <Button variant="primary" onClick={handleUpdateStatus} disabled={actionLoading[`status_${selectedOrder?.id}`] || !newStatus}>
            {actionLoading[`status_${selectedOrder?.id}`] ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Mise à jour...
              </>
            ) : (
              'Mettre à jour'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default OrderList;
